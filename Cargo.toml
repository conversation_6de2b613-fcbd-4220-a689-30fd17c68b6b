[package]
name = "crab-dlna"
version = "0.2.1"
authors = ["<PERSON> <g<PERSON><PERSON><PERSON><PERSON><EMAIL>>"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/gabrielmagno/crab-dlna"
homepage = "https://github.com/gabrielmagno/crab-dlna"
description = "A minimal UPnP/DLNA media streamer"
categories = ["command-line-utilities", "multimedia", "multimedia::video"]
keywords = ["dlna", "upnp", "cli", "stream", "video"]
edition = "2024"
readme = "README.md"

[dependencies]
# Async runtime and utilities
tokio = { version = "1.47.1", features = ["full"] }
futures = "0.3.31"
futures-util = { version = "0.3.31", default-features = false }
pin-utils = "0.1.0"

# Logging
log = "0.4.27"
simple_logger = "5.0.0"

# Command line interface
clap = { version = "4.5.42", features = ["derive"] }

# Terminal and keyboard input
crossterm = "0.29.0"
ratatui = "0.29.0"
tui-input = "0.14.0"

# Network and UPnP/DLNA
rupnp = "3.0.0"
ssdp-client = "2.1.0"
local-ip-address = "0.6.5"
axum = "0.8.4"
tower = "0.5.2"
tower-http = { version = "0.6.6", features = ["fs"] }
http = "1.3.1"

# Subtitle processing
aspasia = "0.2.1"
subtile = "0.4.0"

# Utilities
slugify = "0.1.0"
arboard = "3.6.0"
askama = "0.14.0"
quick-xml = "0.38.0"


[profile.release]
lto = "fat"
codegen-units = 1
